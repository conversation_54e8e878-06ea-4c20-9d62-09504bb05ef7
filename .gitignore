# Dipendenze Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# File di ambiente (contengono API keys)
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# File di configurazione con chiavi API
config.json
secrets.json
*.key

# Log files
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory usato da tools come istanbul
coverage/

# Directory usate da IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Build e dist
dist/
build/

# Temporary files
tmp/
temp/

# Cache files
.cache/
.parcel-cache/

# Test
test-results/
genera-database-completo.py
genera-smorfia-completa.py
data/smorfia-completa.json
data/smorfia-dettagliata.json
data/smorfia-napoletana-completa.json
