<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Debug Microfono Android - Smorf-IA</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1a365d 0%, #553c9a 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .test-section {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            border-left: 4px solid #d69e2e;
        }
        .success { border-left-color: #38a169; background: rgba(56,161,105,0.2); }
        .error { border-left-color: #e53e3e; background: rgba(229,62,62,0.2); }
        .warning { border-left-color: #d69e2e; background: rgba(214,158,46,0.2); }
        button {
            background: linear-gradient(45deg, #d69e2e, #c53030);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px 5px;
            min-width: 200px;
        }
        button:hover { transform: translateY(-2px); box-shadow: 0 4px 12px rgba(0,0,0,0.3); }
        button:disabled { opacity: 0.5; cursor: not-allowed; }
        .status { font-weight: bold; margin: 10px 0; }
        .details { 
            background: rgba(0,0,0,0.3); 
            padding: 15px; 
            border-radius: 8px; 
            font-family: monospace; 
            font-size: 12px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .timer { font-size: 24px; font-weight: bold; color: #d69e2e; }
        .transcript-area {
            background: rgba(255,255,255,0.9);
            color: #333;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            min-height: 100px;
        }
        .permission-box {
            background: rgba(214,158,46,0.2);
            border: 2px solid #d69e2e;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }
        .debug-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .device-info {
            background: rgba(85,60,154,0.2);
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #553c9a;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="text-align: center;">🔧 Debug Microfono Android</h1>
        <p style="text-align: center; opacity: 0.8;">Diagnostica avanzata per problemi microfono su Android</p>

        <!-- Device Info -->
        <div class="device-info">
            <h3>📱 Informazioni Dispositivo</h3>
            <div id="device-info">Caricamento...</div>
        </div>

        <!-- Permission Status -->
        <div class="permission-box">
            <h3>🔒 Stato Permessi</h3>
            <div id="permission-status">Verifica in corso...</div>
            <button id="request-permission">Richiedi Permessi Espliciti</button>
        </div>

        <!-- Test Grid -->
        <div class="debug-grid">
            <!-- Test 1: Basic Audio Access -->
            <div class="test-section" id="audio-test">
                <h3>🎤 Test 1: Accesso Audio Base</h3>
                <button onclick="testBasicAudio()">Testa Accesso Microfono</button>
                <div id="audio-result" class="status"></div>
                <div id="audio-details" class="details"></div>
            </div>

            <!-- Test 2: Speech Recognition -->
            <div class="test-section" id="speech-test">
                <h3>🗣️ Test 2: Speech Recognition</h3>
                <button onclick="testSpeechRecognition()">Testa Speech-to-Text</button>
                <div id="speech-result" class="status"></div>
                <div id="speech-details" class="details"></div>
            </div>

            <!-- Test 3: Android Specific -->
            <div class="test-section" id="android-test">
                <h3>🤖 Test 3: Configurazioni Android</h3>
                <button onclick="testAndroidSpecific()">Test Android Ottimizzato</button>
                <div id="android-result" class="status"></div>
                <div id="android-details" class="details"></div>
            </div>

            <!-- Test 4: Continuous Recording -->
            <div class="test-section" id="continuous-test">
                <h3>🔄 Test 4: Registrazione Continua</h3>
                <button onclick="startContinuousTest()" id="continuous-btn">Avvia Test Continuo</button>
                <div id="continuous-result" class="status"></div>
                <div class="timer" id="continuous-timer">00:00</div>
                <div class="transcript-area" id="continuous-transcript">Trascrizione apparirà qui...</div>
            </div>
        </div>

        <!-- Complete Test -->
        <div class="test-section">
            <h3>🧪 Test Completo Smorf-IA</h3>
            <button onclick="runCompleteTest()">Esegui Test Completo</button>
            <div id="complete-result" class="status"></div>
            <div id="complete-details" class="details"></div>
        </div>

        <!-- Results Summary -->
        <div class="test-section" id="summary" style="display:none;">
            <h3>📊 Riepilogo Diagnostica</h3>
            <div id="summary-content"></div>
        </div>
    </div>

    <script>
        let testResults = {};
        let continuousRecognition = null;
        let continuousTimer = null;
        let continuousSeconds = 0;

        // Initialize
        window.addEventListener('load', () => {
            displayDeviceInfo();
            checkPermissionStatus();
        });

        function displayDeviceInfo() {
            const info = {
                userAgent: navigator.userAgent,
                platform: navigator.platform,
                language: navigator.language,
                cookieEnabled: navigator.cookieEnabled,
                onLine: navigator.onLine,
                hardwareConcurrency: navigator.hardwareConcurrency,
                maxTouchPoints: navigator.maxTouchPoints,
                speechRecognition: !!(window.SpeechRecognition || window.webkitSpeechRecognition),
                mediaDevices: !!navigator.mediaDevices,
                getUserMedia: !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia),
                permissions: !!navigator.permissions,
                isAndroid: /Android/i.test(navigator.userAgent),
                isMobile: /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),
                screen: `${screen.width}x${screen.height}`,
                viewport: `${window.innerWidth}x${window.innerHeight}`,
                pixelRatio: window.devicePixelRatio
            };

            document.getElementById('device-info').innerHTML = Object.entries(info)
                .map(([key, value]) => `<strong>${key}:</strong> ${value}`)
                .join('<br>');
        }

        async function checkPermissionStatus() {
            const statusEl = document.getElementById('permission-status');
            
            try {
                if (navigator.permissions) {
                    const permission = await navigator.permissions.query({ name: 'microphone' });
                    statusEl.innerHTML = `
                        <div class="status">Stato: ${permission.state}</div>
                        <small>granted = concesso, denied = negato, prompt = da richiedere</small>
                    `;
                    
                    if (permission.state === 'denied') {
                        statusEl.innerHTML += '<br><strong style="color: #e53e3e;">⚠️ Permesso negato! Vai in Impostazioni > Privacy > Microfono</strong>';
                    }
                } else {
                    statusEl.innerHTML = 'API Permissions non disponibile su questo browser';
                }
            } catch (error) {
                statusEl.innerHTML = `Errore verifica permessi: ${error.message}`;
            }
        }

        document.getElementById('request-permission').addEventListener('click', async () => {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                document.getElementById('permission-status').innerHTML = '✅ Permessi concessi!';
                stream.getTracks().forEach(track => track.stop());
                checkPermissionStatus();
            } catch (error) {
                document.getElementById('permission-status').innerHTML = `❌ Errore: ${error.message}`;
            }
        });

        async function testBasicAudio() {
            const resultEl = document.getElementById('audio-result');
            const detailsEl = document.getElementById('audio-details');
            
            resultEl.textContent = '🔄 Testing...';
            
            const testData = {
                timestamp: new Date().toISOString(),
                browser: navigator.userAgent,
                test: 'basicAudio'
            };

            try {
                // Test getUserMedia with Android-optimized constraints
                const stream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true,
                        sampleRate: 16000,
                        channelCount: 1,
                        // Android-specific constraints
                        googEchoCancellation: true,
                        googAutoGainControl: true,
                        googNoiseSuppression: true,
                        googHighpassFilter: true
                    }
                });

                const audioTracks = stream.getAudioTracks();
                const track = audioTracks[0];
                const settings = track.getSettings();
                const capabilities = track.getCapabilities ? track.getCapabilities() : {};

                testData.success = true;
                testData.audioTracks = audioTracks.length;
                testData.settings = settings;
                testData.capabilities = capabilities;

                resultEl.textContent = '✅ Accesso microfono riuscito';
                resultEl.className = 'status success';
                
                // Stop the stream
                stream.getTracks().forEach(track => track.stop());
                
                testResults.basicAudio = true;

            } catch (error) {
                testData.success = false;
                testData.error = error.message;
                testData.errorName = error.name;

                resultEl.textContent = `❌ Errore: ${error.message}`;
                resultEl.className = 'status error';
                
                testResults.basicAudio = false;
            }

            detailsEl.textContent = JSON.stringify(testData, null, 2);
            updateSummary();
        }

        async function testSpeechRecognition() {
            const resultEl = document.getElementById('speech-result');
            const detailsEl = document.getElementById('speech-details');
            
            resultEl.textContent = '🔄 Testing...';
            
            const testData = {
                timestamp: new Date().toISOString(),
                test: 'speechRecognition'
            };

            try {
                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                
                if (!SpeechRecognition) {
                    throw new Error('Speech Recognition non supportato');
                }

                const recognition = new SpeechRecognition();
                
                // Android-optimized settings
                recognition.continuous = false;
                recognition.interimResults = false;
                recognition.lang = 'it-IT';
                recognition.maxAlternatives = 1;

                testData.settings = {
                    continuous: recognition.continuous,
                    interimResults: recognition.interimResults,
                    lang: recognition.lang,
                    maxAlternatives: recognition.maxAlternatives
                };

                let testCompleted = false;
                const timeout = setTimeout(() => {
                    if (!testCompleted) {
                        recognition.stop();
                        testData.timeout = true;
                        resultEl.textContent = '⚠️ Timeout - Speech Recognition non ha risposto';
                        resultEl.className = 'status warning';
                        detailsEl.textContent = JSON.stringify(testData, null, 2);
                        testResults.speechRecognition = false;
                        updateSummary();
                    }
                }, 5000);

                recognition.onstart = () => {
                    testData.started = true;
                    resultEl.textContent = '🎤 Speech Recognition avviato - prova a dire "ciao"';
                };

                recognition.onresult = (event) => {
                    testCompleted = true;
                    clearTimeout(timeout);
                    
                    const transcript = event.results[0][0].transcript;
                    const confidence = event.results[0][0].confidence;
                    
                    testData.success = true;
                    testData.transcript = transcript;
                    testData.confidence = confidence;

                    resultEl.textContent = `✅ Trascrizione: "${transcript}" (${Math.round(confidence * 100)}%)`;
                    resultEl.className = 'status success';
                    
                    testResults.speechRecognition = true;
                    detailsEl.textContent = JSON.stringify(testData, null, 2);
                    updateSummary();
                };

                recognition.onerror = (event) => {
                    testCompleted = true;
                    clearTimeout(timeout);
                    
                    testData.success = false;
                    testData.error = event.error;
                    testData.message = event.message;

                    resultEl.textContent = `❌ Errore: ${event.error}`;
                    resultEl.className = 'status error';
                    
                    testResults.speechRecognition = false;
                    detailsEl.textContent = JSON.stringify(testData, null, 2);
                    updateSummary();
                };

                recognition.onend = () => {
                    if (!testCompleted) {
                        testCompleted = true;
                        clearTimeout(timeout);
                        
                        testData.endedWithoutResult = true;
                        resultEl.textContent = '⚠️ Speech Recognition terminato senza risultati';
                        resultEl.className = 'status warning';
                        
                        testResults.speechRecognition = false;
                        detailsEl.textContent = JSON.stringify(testData, null, 2);
                        updateSummary();
                    }
                };

                recognition.start();

            } catch (error) {
                testData.success = false;
                testData.error = error.message;

                resultEl.textContent = `❌ Errore: ${error.message}`;
                resultEl.className = 'status error';
                
                testResults.speechRecognition = false;
                detailsEl.textContent = JSON.stringify(testData, null, 2);
                updateSummary();
            }
        }

        async function testAndroidSpecific() {
            const resultEl = document.getElementById('android-result');
            const detailsEl = document.getElementById('android-details');
            
            resultEl.textContent = '🔄 Testing...';
            
            const testData = {
                timestamp: new Date().toISOString(),
                test: 'androidSpecific',
                isAndroid: /Android/i.test(navigator.userAgent)
            };

            try {
                // Test 1: Android-specific audio constraints
                const stream1 = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true,
                        sampleRate: 16000,
                        channelCount: 1,
                        // Android Chrome specific
                        googEchoCancellation: true,
                        googAutoGainControl: true,
                        googNoiseSuppression: true,
                        googHighpassFilter: true,
                        googTypingNoiseDetection: true,
                        // Additional Android constraints
                        latency: 0.1,
                        volume: 1.0
                    }
                });
                
                testData.androidConstraints = 'success';
                stream1.getTracks().forEach(track => track.stop());

                // Test 2: Multiple short recordings (Android pattern)
                let shortRecordings = 0;
                for (let i = 0; i < 3; i++) {
                    try {
                        const stream2 = await navigator.mediaDevices.getUserMedia({
                            audio: { sampleRate: 16000, channelCount: 1 }
                        });
                        shortRecordings++;
                        stream2.getTracks().forEach(track => track.stop());
                        // Small delay between recordings
                        await new Promise(resolve => setTimeout(resolve, 200));
                    } catch (error) {
                        break;
                    }
                }
                
                testData.shortRecordings = shortRecordings;

                // Test 3: Check Android-specific APIs
                testData.androidAPIs = {
                    speechSynthesis: !!window.speechSynthesis,
                    webkitSpeechRecognition: !!window.webkitSpeechRecognition,
                    chrome: !!window.chrome,
                    mediaDevices: !!navigator.mediaDevices,
                    permissions: !!navigator.permissions
                };

                if (shortRecordings >= 2) {
                    resultEl.textContent = '✅ Configurazioni Android ottimali';
                    resultEl.className = 'status success';
                    testResults.androidSpecific = true;
                } else {
                    resultEl.textContent = '⚠️ Alcune limitazioni Android rilevate';
                    resultEl.className = 'status warning';
                    testResults.androidSpecific = false;
                }

            } catch (error) {
                testData.error = error.message;
                testData.errorName = error.name;

                resultEl.textContent = `❌ Errore: ${error.message}`;
                resultEl.className = 'status error';
                testResults.androidSpecific = false;
            }

            detailsEl.textContent = JSON.stringify(testData, null, 2);
            updateSummary();
        }

        async function startContinuousTest() {
            const btn = document.getElementById('continuous-btn');
            const resultEl = document.getElementById('continuous-result');
            const timerEl = document.getElementById('continuous-timer');
            const transcriptEl = document.getElementById('continuous-transcript');

            if (continuousRecognition && continuousRecognition.isActive) {
                // Stop test
                stopContinuousTest();
                return;
            }

            try {
                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                continuousRecognition = new SpeechRecognition();
                
                // Android-optimized settings for continuous recording
                continuousRecognition.continuous = false; // Better for Android
                continuousRecognition.interimResults = false;
                continuousRecognition.lang = 'it-IT';
                continuousRecognition.maxAlternatives = 1;
                continuousRecognition.isActive = true;

                let fullTranscript = '';
                let restartCount = 0;

                continuousRecognition.onstart = () => {
                    resultEl.textContent = '🎤 Registrazione continua attiva (Android)';
                    resultEl.className = 'status success';
                    btn.textContent = 'Ferma Test Continuo';
                    startContinuousTimer();
                };

                continuousRecognition.onresult = (event) => {
                    const transcript = event.results[0][0].transcript;
                    fullTranscript += transcript + ' ';
                    transcriptEl.textContent = fullTranscript;
                    
                    // Auto-restart for continuous effect on Android
                    setTimeout(() => {
                        if (continuousRecognition && continuousRecognition.isActive) {
                            try {
                                continuousRecognition.start();
                                restartCount++;
                            } catch (error) {
                                console.log('Restart error:', error.message);
                            }
                        }
                    }, 100);
                };

                continuousRecognition.onerror = (event) => {
                    if (event.error === 'no-speech' && continuousRecognition.isActive) {
                        // Continue on no-speech for Android
                        setTimeout(() => {
                            if (continuousRecognition && continuousRecognition.isActive) {
                                try {
                                    continuousRecognition.start();
                                } catch (error) {
                                    console.log('No-speech restart error:', error.message);
                                }
                            }
                        }, 100);
                        return;
                    }
                    
                    resultEl.textContent = `❌ Errore: ${event.error}`;
                    resultEl.className = 'status error';
                    stopContinuousTest();
                };

                continuousRecognition.onend = () => {
                    if (continuousRecognition && continuousRecognition.isActive) {
                        // Auto-restart on end for Android
                        setTimeout(() => {
                            if (continuousRecognition && continuousRecognition.isActive) {
                                try {
                                    continuousRecognition.start();
                                    restartCount++;
                                } catch (error) {
                                    console.log('End restart error:', error.message);
                                }
                            }
                        }, 100);
                    }
                };

                continuousRecognition.start();
                testResults.continuous = true;

            } catch (error) {
                resultEl.textContent = `❌ Errore: ${error.message}`;
                resultEl.className = 'status error';
                testResults.continuous = false;
            }

            updateSummary();
        }

        function stopContinuousTest() {
            if (continuousRecognition) {
                continuousRecognition.isActive = false;
                try {
                    continuousRecognition.stop();
                } catch (error) {
                    console.log('Stop error:', error.message);
                }
                continuousRecognition = null;
            }

            if (continuousTimer) {
                clearInterval(continuousTimer);
                continuousTimer = null;
            }

            const btn = document.getElementById('continuous-btn');
            const resultEl = document.getElementById('continuous-result');
            
            btn.textContent = 'Avvia Test Continuo';
            resultEl.textContent = '⏹️ Test continuo fermato';
            resultEl.className = 'status';
        }

        function startContinuousTimer() {
            continuousSeconds = 0;
            const timerEl = document.getElementById('continuous-timer');
            
            continuousTimer = setInterval(() => {
                continuousSeconds++;
                const minutes = Math.floor(continuousSeconds / 60);
                const seconds = continuousSeconds % 60;
                timerEl.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }, 1000);
        }

        async function runCompleteTest() {
            const resultEl = document.getElementById('complete-result');
            const detailsEl = document.getElementById('complete-details');
            
            resultEl.textContent = '🔄 Eseguendo test completo...';
            
            // Run all tests in sequence
            await testBasicAudio();
            await new Promise(resolve => setTimeout(resolve, 1000));
            await testSpeechRecognition();
            await new Promise(resolve => setTimeout(resolve, 1000));
            await testAndroidSpecific();
            
            const passed = Object.values(testResults).filter(Boolean).length;
            const total = Object.keys(testResults).length;
            
            if (passed === total) {
                resultEl.textContent = `✅ Test completo: ${passed}/${total} superati`;
                resultEl.className = 'status success';
            } else {
                resultEl.textContent = `⚠️ Test completo: ${passed}/${total} superati`;
                resultEl.className = 'status warning';
            }
            
            detailsEl.textContent = JSON.stringify(testResults, null, 2);
        }

        function updateSummary() {
            const summaryEl = document.getElementById('summary');
            const contentEl = document.getElementById('summary-content');
            
            if (Object.keys(testResults).length === 0) return;
            
            summaryEl.style.display = 'block';
            
            const passed = Object.values(testResults).filter(Boolean).length;
            const total = Object.keys(testResults).length;
            
            let html = `<h4>📊 Risultati: ${passed}/${total} test superati</h4><ul>`;
            
            Object.entries(testResults).forEach(([test, result]) => {
                const icon = result ? '✅' : '❌';
                html += `<li>${icon} ${test}: ${result ? 'OK' : 'FAILED'}</li>`;
            });
            
            html += '</ul>';
            
            if (passed < total) {
                html += '<h4>🔧 Raccomandazioni:</h4><ul>';
                if (!testResults.basicAudio) {
                    html += '<li>Verifica permessi microfono in Impostazioni Chrome</li>';
                }
                if (!testResults.speechRecognition) {
                    html += '<li>Prova a riavviare il browser o usare Chrome più recente</li>';
                }
                if (!testResults.androidSpecific) {
                    html += '<li>Chiudi altre app che usano il microfono</li>';
                }
                html += '</ul>';
            }
            
            contentEl.innerHTML = html;
        }
    </script>
</body>
</html>