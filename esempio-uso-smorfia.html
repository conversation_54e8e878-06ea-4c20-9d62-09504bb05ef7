<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Esempio Uso Smorfia JSON - Smorf-IA</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #4a5568;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            background: #f7fafc;
        }
        .section h2 {
            color: #2d3748;
            margin-top: 0;
            border-bottom: 2px solid #4299e1;
            padding-bottom: 10px;
        }
        input, textarea, select {
            width: 100%;
            padding: 12px;
            border: 2px solid #cbd5e0;
            border-radius: 8px;
            font-size: 16px;
            margin: 10px 0;
            box-sizing: border-box;
        }
        input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #4299e1;
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
        }
        button {
            background: linear-gradient(135deg, #4299e1, #3182ce);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(66, 153, 225, 0.4);
        }
        .risultati {
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            min-height: 100px;
            max-height: 300px;
            overflow-y: auto;
        }
        .numero {
            display: inline-block;
            background: #4299e1;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
            margin: 3px;
            font-weight: bold;
        }
        .parola-trovata {
            background: #e6fffa;
            border-left: 4px solid #38b2ac;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
        }
        .statistiche {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            display: block;
        }
        .loading {
            text-align: center;
            color: #718096;
            font-style: italic;
        }
        .error {
            background: #fed7d7;
            color: #c53030;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            background: #c6f6d5;
            color: #22543d;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎭 Esempio Uso Smorfia JSON</h1>
        <p style="text-align: center; font-size: 1.2em; color: #718096;">
            Dimostra l'utilizzo del nuovo formato JSON organizzato per lettere
        </p>

        <!-- Statistiche -->
        <div class="section">
            <h2>📊 Statistiche Dati</h2>
            <div id="statistiche" class="loading">Caricamento statistiche...</div>
        </div>

        <!-- Ricerca Parola -->
        <div class="section">
            <h2>🔍 Ricerca Parola</h2>
            <input type="text" id="input-parola" placeholder="Inserisci una parola (es: casa, amore, morte)">
            <button onclick="cercaParola()">Cerca Parola</button>
            <div id="risultati-parola" class="risultati"></div>
        </div>

        <!-- Ricerca Termine -->
        <div class="section">
            <h2>🔎 Ricerca Termine (Parziale)</h2>
            <input type="text" id="input-termine" placeholder="Inserisci un termine (es: cas, amor, mort)">
            <button onclick="cercaTermine()">Cerca Termine</button>
            <div id="risultati-termine" class="risultati"></div>
        </div>

        <!-- Ricerca per Lettera -->
        <div class="section">
            <h2>📝 Parole per Lettera</h2>
            <select id="select-lettera">
                <option value="">Seleziona una lettera...</option>
            </select>
            <button onclick="mostraParoleLettera()">Mostra Parole</button>
            <div id="risultati-lettera" class="risultati"></div>
        </div>

        <!-- Ricerca Numero -->
        <div class="section">
            <h2>🔢 Ricerca per Numero</h2>
            <input type="number" id="input-numero" min="1" max="90" placeholder="Inserisci un numero (1-90)">
            <button onclick="cercaNumero()">Cerca Numero</button>
            <div id="risultati-numero" class="risultati"></div>
        </div>

        <!-- Analisi Testo -->
        <div class="section">
            <h2>📖 Analisi Testo</h2>
            <textarea id="input-testo" rows="4" placeholder="Inserisci un testo da analizzare (es: Ho sognato una casa bianca con un giardino pieno di fiori)"></textarea>
            <button onclick="analizzaTesto()">Analizza Testo</button>
            <div id="risultati-testo" class="risultati"></div>
        </div>

        <!-- Generazione Numeri -->
        <div class="section">
            <h2>🎲 Genera Numeri da Parole</h2>
            <input type="text" id="input-parole-chiave" placeholder="Inserisci parole separate da virgola (es: casa, amore, fortuna)">
            <input type="number" id="input-quantita" min="1" max="10" value="5" placeholder="Quantità numeri">
            <button onclick="generaNumeri()">Genera Numeri</button>
            <div id="risultati-generazione" class="risultati"></div>
        </div>
    </div>

    <script type="module">
        import { SmorfiaUtils } from './js/smorfia-utils.js';

        let smorfiaUtils;

        // Inizializza l'applicazione
        async function init() {
            try {
                smorfiaUtils = new SmorfiaUtils();
                
                // Aspetta che i dati siano caricati
                while (!smorfiaUtils.isDataLoaded()) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                }

                // Mostra statistiche
                mostraStatistiche();
                
                // Popola select lettere
                popolaSelectLettere();
                
                console.log('✅ Applicazione inizializzata correttamente');
            } catch (error) {
                console.error('❌ Errore inizializzazione:', error);
                document.getElementById('statistiche').innerHTML = 
                    '<div class="error">Errore nel caricamento dei dati</div>';
            }
        }

        function mostraStatistiche() {
            const stats = smorfiaUtils.getStatistiche();
            const container = document.getElementById('statistiche');
            
            container.innerHTML = `
                <div class="statistiche">
                    <div class="stat-card">
                        <span class="stat-number">${stats.totaleParole}</span>
                        <div>Parole Totali</div>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number">${stats.totaleLettere}</span>
                        <div>Lettere Disponibili</div>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number">90</span>
                        <div>Numeri Smorfia</div>
                    </div>
                    <div class="stat-card">
                        <span class="stat-number">${stats.metadata?.version || '1.0'}</span>
                        <div>Versione</div>
                    </div>
                </div>
            `;
        }

        function popolaSelectLettere() {
            const select = document.getElementById('select-lettera');
            const lettere = smorfiaUtils.getLettereDisponibili();
            
            lettere.forEach(lettera => {
                const option = document.createElement('option');
                option.value = lettera;
                option.textContent = lettera;
                select.appendChild(option);
            });
        }

        // Funzioni globali per i pulsanti
        window.cercaParola = function() {
            const parola = document.getElementById('input-parola').value.trim();
            if (!parola) return;

            const numeri = smorfiaUtils.cercaParola(parola);
            const container = document.getElementById('risultati-parola');
            
            if (numeri.length > 0) {
                container.innerHTML = `
                    <div class="success">
                        <strong>Parola trovata:</strong> ${parola}<br>
                        <strong>Numeri:</strong> ${numeri.map(n => `<span class="numero">${n}</span>`).join('')}
                    </div>
                `;
            } else {
                container.innerHTML = `<div class="error">Nessun numero trovato per "${parola}"</div>`;
            }
        };

        window.cercaTermine = function() {
            const termine = document.getElementById('input-termine').value.trim();
            if (!termine) return;

            const risultati = smorfiaUtils.cercaTermine(termine);
            const container = document.getElementById('risultati-termine');
            
            if (risultati.length > 0) {
                container.innerHTML = risultati.map(r => `
                    <div class="parola-trovata">
                        <strong>${r.parola}</strong> (${r.lettera}): 
                        ${r.numeri.map(n => `<span class="numero">${n}</span>`).join('')}
                    </div>
                `).join('');
            } else {
                container.innerHTML = `<div class="error">Nessuna parola trovata per "${termine}"</div>`;
            }
        };

        window.mostraParoleLettera = function() {
            const lettera = document.getElementById('select-lettera').value;
            if (!lettera) return;

            const parole = smorfiaUtils.getParolePerLettera(lettera);
            const container = document.getElementById('risultati-lettera');
            
            const paroleArray = Object.entries(parole);
            if (paroleArray.length > 0) {
                container.innerHTML = `
                    <div class="success">
                        <strong>Lettera ${lettera}:</strong> ${paroleArray.length} parole trovate
                    </div>
                    ${paroleArray.slice(0, 20).map(([parola, numeri]) => `
                        <div class="parola-trovata">
                            <strong>${parola}:</strong> 
                            ${numeri.map(n => `<span class="numero">${n}</span>`).join('')}
                        </div>
                    `).join('')}
                    ${paroleArray.length > 20 ? '<div style="text-align: center; margin: 10px; color: #718096;">... e altre ' + (paroleArray.length - 20) + ' parole</div>' : ''}
                `;
            } else {
                container.innerHTML = `<div class="error">Nessuna parola trovata per la lettera "${lettera}"</div>`;
            }
        };

        window.cercaNumero = function() {
            const numero = parseInt(document.getElementById('input-numero').value);
            if (!numero || numero < 1 || numero > 90) return;

            const risultati = smorfiaUtils.cercaNumero(numero);
            const container = document.getElementById('risultati-numero');
            
            if (risultati.length > 0) {
                container.innerHTML = `
                    <div class="success">
                        <strong>Numero ${numero}:</strong> ${risultati.length} parole trovate
                    </div>
                    ${risultati.map(r => `
                        <div class="parola-trovata">
                            <strong>${r.parola}</strong> (${r.lettera}): 
                            ${r.numeri.map(n => `<span class="numero">${n}</span>`).join('')}
                        </div>
                    `).join('')}
                `;
            } else {
                container.innerHTML = `<div class="error">Nessuna parola trovata per il numero ${numero}</div>`;
            }
        };

        window.analizzaTesto = function() {
            const testo = document.getElementById('input-testo').value.trim();
            if (!testo) return;

            const risultati = smorfiaUtils.analizzaTesto(testo);
            const container = document.getElementById('risultati-testo');
            
            if (risultati.length > 0) {
                const tuttiNumeri = [...new Set(risultati.flatMap(r => r.numeri))].sort((a, b) => a - b);
                container.innerHTML = `
                    <div class="success">
                        <strong>Analisi completata:</strong> ${risultati.length} parole trovate<br>
                        <strong>Numeri estratti:</strong> ${tuttiNumeri.map(n => `<span class="numero">${n}</span>`).join('')}
                    </div>
                    ${risultati.map(r => `
                        <div class="parola-trovata">
                            <strong>${r.parola}</strong>: 
                            ${r.numeri.map(n => `<span class="numero">${n}</span>`).join('')}
                        </div>
                    `).join('')}
                `;
            } else {
                container.innerHTML = `<div class="error">Nessuna parola della Smorfia trovata nel testo</div>`;
            }
        };

        window.generaNumeri = function() {
            const paroleInput = document.getElementById('input-parole-chiave').value.trim();
            const quantita = parseInt(document.getElementById('input-quantita').value) || 5;
            
            if (!paroleInput) return;

            const paroleChiave = paroleInput.split(',').map(p => p.trim()).filter(p => p);
            const numeri = smorfiaUtils.generaNumeriDaParole(paroleChiave, quantita);
            const container = document.getElementById('risultati-generazione');
            
            container.innerHTML = `
                <div class="success">
                    <strong>Parole chiave:</strong> ${paroleChiave.join(', ')}<br>
                    <strong>Numeri generati:</strong> ${numeri.map(n => `<span class="numero">${n}</span>`).join('')}
                </div>
            `;
        };

        // Inizializza l'applicazione
        init();
    </script>
</body>
</html>
