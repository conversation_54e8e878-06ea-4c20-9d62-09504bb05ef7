<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 Test Microfono Android</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #1a365d; color: white; text-align: center; }
        button { background: #d69e2e; color: white; border: none; padding: 15px 30px; border-radius: 8px; font-size: 18px; margin: 10px; cursor: pointer; }
        button:hover { background: #c53030; }
        .result { background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px; margin: 20px; }
        .success { border-left: 4px solid #38a169; }
        .error { border-left: 4px solid #e53e3e; }
        .transcript { background: rgba(255,255,255,0.9); color: #333; padding: 15px; border-radius: 8px; margin: 20px; min-height: 50px; }
    </style>
</head>
<body>
    <h1>🤖 Test Microfono Android</h1>
    <p><PERSON><PERSON><PERSON> per testare il microfono ottimizzato per Android</p>
    
    <button onclick="testMicrophone()">🎤 Testa Microfono</button>
    <button onclick="testSpeech()">🗣️ Testa Speech-to-Text</button>
    
    <div id="result" class="result" style="display:none;"></div>
    <div id="transcript" class="transcript" style="display:none;">Parla qui...</div>
    
    <script>
        let recognition = null;
        let isRecording = false;
        
        async function testMicrophone() {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '🔄 Testing microfono Android...';
            
            try {
                const constraints = {
                    audio: {
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true,
                        sampleRate: { ideal: 16000 },
                        channelCount: { ideal: 1 },
                        googEchoCancellation: true,
                        googAutoGainControl: true,
                        googNoiseSuppression: true,
                        googHighpassFilter: true,
                        latency: { ideal: 0.1 }
                    }
                };
                
                const stream = await navigator.mediaDevices.getUserMedia(constraints);
                const tracks = stream.getAudioTracks();
                const settings = tracks[0].getSettings();
                
                resultDiv.innerHTML = `✅ Microfono Android OK!<br>
                    Tracks: ${tracks.length}<br>
                    Sample Rate: ${settings.sampleRate}<br>
                    Channels: ${settings.channelCount}`;
                resultDiv.className = 'result success';
                
                // Stop stream
                stream.getTracks().forEach(track => track.stop());
                
            } catch (error) {
                resultDiv.innerHTML = `❌ Errore Android: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        async function testSpeech() {
            const resultDiv = document.getElementById('result');
            const transcriptDiv = document.getElementById('transcript');
            
            if (isRecording) {
                stopSpeech();
                return;
            }
            
            try {
                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                if (!SpeechRecognition) {
                    throw new Error('Speech Recognition non supportato');
                }
                
                recognition = new SpeechRecognition();
                recognition.continuous = false; // Android ottimizzato
                recognition.interimResults = false;
                recognition.lang = 'it-IT';
                recognition.maxAlternatives = 1;
                
                let restartCount = 0;
                let fullTranscript = '';
                
                recognition.onstart = () => {
                    isRecording = true;
                    resultDiv.style.display = 'block';
                    resultDiv.innerHTML = '🎤 Registrazione Android attiva - Parla!';
                    resultDiv.className = 'result success';
                    transcriptDiv.style.display = 'block';
                    document.querySelector('button[onclick="testSpeech()"]').textContent = '🛑 Ferma';
                };
                
                recognition.onresult = (event) => {
                    const transcript = event.results[0][0].transcript;
                    fullTranscript += transcript + ' ';
                    transcriptDiv.textContent = fullTranscript;
                    
                    // Auto-restart per Android
                    if (isRecording && restartCount < 10) {
                        setTimeout(() => {
                            if (isRecording) {
                                try {
                                    recognition.start();
                                    restartCount++;
                                } catch (e) { console.log('Restart:', e.message); }
                            }
                        }, 100);
                    }
                };
                
                recognition.onerror = (event) => {
                    if (event.error === 'no-speech' && isRecording) {
                        console.log('No speech, continuando...');
                        setTimeout(() => {
                            if (isRecording) {
                                try { recognition.start(); } catch (e) {}
                            }
                        }, 100);
                        return;
                    }
                    
                    resultDiv.innerHTML = `❌ Errore Speech Android: ${event.error}`;
                    resultDiv.className = 'result error';
                    stopSpeech();
                };
                
                recognition.onend = () => {
                    if (isRecording && restartCount < 10) {
                        setTimeout(() => {
                            if (isRecording) {
                                try {
                                    recognition.start();
                                    restartCount++;
                                } catch (e) { console.log('End restart:', e.message); }
                            }
                        }, 100);
                    }
                };
                
                recognition.start();
                
            } catch (error) {
                resultDiv.style.display = 'block';
                resultDiv.innerHTML = `❌ Errore: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        function stopSpeech() {
            isRecording = false;
            if (recognition) {
                try { recognition.stop(); } catch (e) {}
                recognition = null;
            }
            document.querySelector('button[onclick="testSpeech()"]').textContent = '🗣️ Testa Speech-to-Text';
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '⏹️ Registrazione fermata';
            resultDiv.className = 'result';
        }
        
        // Auto-detect Android
        if (/Android/i.test(navigator.userAgent)) {
            document.body.style.background = '#2d3748';
            document.querySelector('h1').innerHTML += ' (Android rilevato ✅)';
        }
    </script>
</body>
</html>