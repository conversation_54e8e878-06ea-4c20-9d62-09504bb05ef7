<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Audio Mobile - Smorf-IA</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.warning { background: #fff3cd; color: #856404; }
        .status.info { background: #d1ecf1; color: #0c5460; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .device-info {
            background: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎤 Test Audio Mobile - Smorf-IA</h1>
        <p>Questo strumento testa la compatibilità audio su dispositivi mobili</p>

        <!-- Device Info -->
        <div class="test-section">
            <h3>📱 Informazioni Dispositivo</h3>
            <div id="device-info" class="device-info"></div>
        </div>

        <!-- Browser Support Test -->
        <div class="test-section">
            <h3>🌐 Test Supporto Browser</h3>
            <button class="test-button" onclick="testBrowserSupport()">Testa Supporto</button>
            <div id="browser-support-status"></div>
        </div>

        <!-- Permission Test -->
        <div class="test-section">
            <h3>🔐 Test Permessi Microfono</h3>
            <button class="test-button" onclick="testMicrophonePermissions()">Richiedi Permessi</button>
            <div id="permission-status"></div>
        </div>

        <!-- getUserMedia Test -->
        <div class="test-section">
            <h3>🎙️ Test getUserMedia</h3>
            <button class="test-button" onclick="testGetUserMedia()">Test Accesso Microfono</button>
            <button class="test-button" onclick="stopMediaStream()" disabled id="stop-media-btn">Ferma Stream</button>
            <div id="media-status"></div>
        </div>

        <!-- Speech Recognition Test -->
        <div class="test-section">
            <h3>🗣️ Test Speech Recognition</h3>
            <button class="test-button" onclick="testSpeechRecognition()">Test Speech Recognition</button>
            <button class="test-button" onclick="stopSpeechRecognition()" disabled id="stop-speech-btn">Ferma Recognition</button>
            <div id="speech-status"></div>
            <div id="speech-transcript" class="log" style="display: none;"></div>
        </div>

        <!-- Mobile-Specific Test -->
        <div class="test-section">
            <h3>📱 Test Specifici Mobile</h3>
            <button class="test-button" onclick="testMobileSpecific()">Test Mobile</button>
            <div id="mobile-status"></div>
        </div>

        <!-- Combined Test -->
        <div class="test-section">
            <h3>🔄 Test Completo (Come Smorf-IA)</h3>
            <button class="test-button" onclick="testComplete()">Test Completo</button>
            <button class="test-button" onclick="stopComplete()" disabled id="stop-complete-btn">Ferma Test</button>
            <div id="complete-status"></div>
            <div id="complete-transcript" class="log" style="display: none;"></div>
        </div>

        <!-- Debug Log -->
        <div class="test-section">
            <h3>🐛 Log Debug</h3>
            <button class="test-button" onclick="clearLog()">Pulisci Log</button>
            <div id="debug-log" class="log"></div>
        </div>
    </div>

    <script>
        let mediaStream = null;
        let recognition = null;
        let completeRecognition = null;
        let isRecording = false;

        // Utility functions
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('debug-log');
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function clearLog() {
            document.getElementById('debug-log').innerHTML = '';
        }

        function detectMobile() {
            return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                   (navigator.maxTouchPoints && navigator.maxTouchPoints > 2 && /MacIntel/.test(navigator.platform));
        }

        // Initialize device info
        function initDeviceInfo() {
            const isMobile = detectMobile();
            const info = `
                <strong>User Agent:</strong> ${navigator.userAgent}<br>
                <strong>È Mobile:</strong> ${isMobile ? 'Sì' : 'No'}<br>
                <strong>Touch Points:</strong> ${navigator.maxTouchPoints || 'N/A'}<br>
                <strong>Platform:</strong> ${navigator.platform}<br>
                <strong>Language:</strong> ${navigator.language}<br>
                <strong>Online:</strong> ${navigator.onLine ? 'Sì' : 'No'}<br>
                <strong>HTTPS:</strong> ${location.protocol === 'https:' ? 'Sì' : 'No'}
            `;
            document.getElementById('device-info').innerHTML = info;
            log(`Dispositivo rilevato: ${isMobile ? 'Mobile' : 'Desktop'}`);
        }

        // Test browser support
        function testBrowserSupport() {
            log('Testando supporto browser...');

            const tests = {
                'getUserMedia': !!navigator.mediaDevices?.getUserMedia,
                'SpeechRecognition': !!(window.SpeechRecognition || window.webkitSpeechRecognition),
                'Permissions API': !!navigator.permissions,
                'Service Worker': 'serviceWorker' in navigator,
                'IndexedDB': 'indexedDB' in window,
                'Web Share API': !!navigator.share
            };

            let html = '<ul>';
            for (const [test, supported] of Object.entries(tests)) {
                const status = supported ? '✅' : '❌';
                html += `<li>${status} ${test}</li>`;
                log(`${test}: ${supported ? 'Supportato' : 'Non supportato'}`);
            }
            html += '</ul>';

            showStatus('browser-support-status', html, 'info');
        }

        // Test microphone permissions
        async function testMicrophonePermissions() {
            log('Testando permessi microfono...');

            try {
                if (navigator.permissions) {
                    const permission = await navigator.permissions.query({ name: 'microphone' });
                    showStatus('permission-status', `Stato permesso: ${permission.state}`,
                              permission.state === 'granted' ? 'success' : 'warning');
                    log(`Permesso microfono: ${permission.state}`);
                } else {
                    showStatus('permission-status', 'Permissions API non supportata', 'warning');
                    log('Permissions API non disponibile');
                }
            } catch (error) {
                showStatus('permission-status', `Errore: ${error.message}`, 'error');
                log(`Errore permessi: ${error.message}`);
            }
        }

        // Test getUserMedia
        async function testGetUserMedia() {
            log('Testando getUserMedia...');

            try {
                mediaStream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true
                    }
                });

                showStatus('media-status', 'Accesso microfono riuscito!', 'success');
                document.getElementById('stop-media-btn').disabled = false;
                log('getUserMedia: Successo');

                // Show stream info
                const track = mediaStream.getAudioTracks()[0];
                if (track) {
                    const settings = track.getSettings();
                    log(`Audio track: ${JSON.stringify(settings)}`);
                }

            } catch (error) {
                showStatus('media-status', `Errore: ${error.name} - ${error.message}`, 'error');
                log(`getUserMedia errore: ${error.name} - ${error.message}`);
            }
        }

        function stopMediaStream() {
            if (mediaStream) {
                mediaStream.getTracks().forEach(track => track.stop());
                mediaStream = null;
                document.getElementById('stop-media-btn').disabled = true;
                showStatus('media-status', 'Stream fermato', 'info');
                log('Media stream fermato');
            }
        }

        // Test Speech Recognition
        function testSpeechRecognition() {
            log('Testando Speech Recognition...');

            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            if (!SpeechRecognition) {
                showStatus('speech-status', 'Speech Recognition non supportato', 'error');
                log('Speech Recognition non disponibile');
                return;
            }

            try {
                recognition = new SpeechRecognition();
                recognition.continuous = detectMobile() ? false : true;
                recognition.interimResults = detectMobile() ? false : true;
                recognition.lang = 'it-IT';

                recognition.onstart = () => {
                    showStatus('speech-status', 'Speech Recognition avviato', 'success');
                    document.getElementById('stop-speech-btn').disabled = false;
                    document.getElementById('speech-transcript').style.display = 'block';
                    log('Speech Recognition avviato');
                };

                recognition.onresult = (event) => {
                    let transcript = '';
                    for (let i = 0; i < event.results.length; i++) {
                        transcript += event.results[i][0].transcript;
                    }
                    document.getElementById('speech-transcript').innerHTML = transcript;
                    log(`Trascrizione: ${transcript}`);
                };

                recognition.onerror = (event) => {
                    showStatus('speech-status', `Errore: ${event.error}`, 'error');
                    log(`Speech Recognition errore: ${event.error}`);
                };

                recognition.onend = () => {
                    showStatus('speech-status', 'Speech Recognition terminato', 'info');
                    document.getElementById('stop-speech-btn').disabled = true;
                    log('Speech Recognition terminato');
                };

                recognition.start();

            } catch (error) {
                showStatus('speech-status', `Errore: ${error.message}`, 'error');
                log(`Speech Recognition errore: ${error.message}`);
            }
        }

        function stopSpeechRecognition() {
            if (recognition) {
                recognition.stop();
                recognition = null;
                document.getElementById('stop-speech-btn').disabled = true;
                log('Speech Recognition fermato manualmente');
            }
        }

        // Test mobile-specific features
        function testMobileSpecific() {
            log('Testando funzionalità specifiche mobile...');

            const isMobile = detectMobile();
            const tests = [];

            // Test touch events
            tests.push(`Touch Events: ${('ontouchstart' in window) ? 'Supportati' : 'Non supportati'}`);

            // Test device orientation
            tests.push(`Device Orientation: ${('DeviceOrientationEvent' in window) ? 'Supportato' : 'Non supportato'}`);

            // Test vibration
            tests.push(`Vibration API: ${('vibrate' in navigator) ? 'Supportata' : 'Non supportata'}`);

            // Test network info
            if (navigator.connection) {
                tests.push(`Connessione: ${navigator.connection.effectiveType || 'Sconosciuta'}`);
            }

            // Test PWA features
            tests.push(`Standalone Mode: ${window.matchMedia('(display-mode: standalone)').matches ? 'Sì' : 'No'}`);

            const html = '<ul><li>' + tests.join('</li><li>') + '</li></ul>';
            showStatus('mobile-status', html, 'info');

            tests.forEach(test => log(test));
        }

        // Complete test (simulating Smorf-IA behavior)
        async function testComplete() {
            log('Avviando test completo (simulazione Smorf-IA)...');

            try {
                // Step 1: Request permissions
                if (navigator.permissions) {
                    const permission = await navigator.permissions.query({ name: 'microphone' });
                    log(`Permesso microfono: ${permission.state}`);
                }

                // Step 2: Get media stream
                const stream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true,
                        ...(detectMobile() && {
                            sampleRate: 16000,
                            channelCount: 1
                        })
                    }
                });

                log('Media stream ottenuto');

                // Step 3: Setup speech recognition
                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                if (!SpeechRecognition) {
                    throw new Error('Speech Recognition non supportato');
                }

                completeRecognition = new SpeechRecognition();

                if (detectMobile()) {
                    completeRecognition.continuous = false;
                    completeRecognition.interimResults = false;
                    log('Configurazione mobile attivata');
                } else {
                    completeRecognition.continuous = true;
                    completeRecognition.interimResults = true;
                }

                completeRecognition.lang = 'it-IT';
                completeRecognition.maxAlternatives = 1;

                completeRecognition.onstart = () => {
                    showStatus('complete-status', 'Test completo avviato - Parla ora!', 'success');
                    document.getElementById('stop-complete-btn').disabled = false;
                    document.getElementById('complete-transcript').style.display = 'block';
                    isRecording = true;
                    log('Test completo avviato');
                };

                completeRecognition.onresult = (event) => {
                    let finalTranscript = '';
                    for (let i = event.resultIndex; i < event.results.length; i++) {
                        if (event.results[i].isFinal) {
                            finalTranscript += event.results[i][0].transcript;
                        }
                    }
                    if (finalTranscript) {
                        document.getElementById('complete-transcript').innerHTML += finalTranscript + '\n';
                        log(`Trascrizione finale: ${finalTranscript}`);

                        // Auto-restart on mobile
                        if (detectMobile() && isRecording) {
                            setTimeout(() => {
                                if (isRecording) {
                                    try {
                                        completeRecognition.start();
                                        log('Riavvio automatico mobile');
                                    } catch (error) {
                                        log(`Errore riavvio: ${error.message}`);
                                    }
                                }
                            }, 100);
                        }
                    }
                };

                completeRecognition.onerror = (event) => {
                    log(`Errore: ${event.error}`);
                    if (event.error === 'no-speech' && detectMobile() && isRecording) {
                        // Don't stop on no-speech for mobile
                        setTimeout(() => {
                            if (isRecording) {
                                try {
                                    completeRecognition.start();
                                    log('Riavvio dopo no-speech');
                                } catch (error) {
                                    log(`Errore riavvio no-speech: ${error.message}`);
                                }
                            }
                        }, 100);
                        return;
                    }
                    showStatus('complete-status', `Errore: ${event.error}`, 'error');
                };

                completeRecognition.onend = () => {
                    log('Speech Recognition terminato');
                    if (isRecording && detectMobile()) {
                        // Auto-restart on mobile
                        setTimeout(() => {
                            if (isRecording) {
                                try {
                                    completeRecognition.start();
                                    log('Riavvio automatico onend');
                                } catch (error) {
                                    log(`Errore riavvio onend: ${error.message}`);
                                }
                            }
                        }, 100);
                    }
                };

                // Start recognition
                completeRecognition.start();

                // Set timeout for mobile (30 seconds)
                if (detectMobile()) {
                    setTimeout(() => {
                        if (isRecording) {
                            log('Timeout mobile raggiunto, riavvio...');
                            try {
                                completeRecognition.stop();
                                setTimeout(() => {
                                    if (isRecording) {
                                        completeRecognition.start();
                                    }
                                }, 100);
                            } catch (error) {
                                log(`Errore timeout: ${error.message}`);
                            }
                        }
                    }, 30000);
                }

            } catch (error) {
                showStatus('complete-status', `Errore: ${error.message}`, 'error');
                log(`Errore test completo: ${error.message}`);
            }
        }

        function stopComplete() {
            isRecording = false;
            if (completeRecognition) {
                completeRecognition.stop();
                completeRecognition = null;
            }
            document.getElementById('stop-complete-btn').disabled = true;
            showStatus('complete-status', 'Test completo fermato', 'info');
            log('Test completo fermato');
        }

        // Initialize on load
        window.addEventListener('load', () => {
            initDeviceInfo();
            log('Test mobile audio inizializzato');
        });
    </script>
</body>
</html>
