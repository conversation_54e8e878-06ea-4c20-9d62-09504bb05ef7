# 📚 Smorfia JSON - Formato Strutturato

## 🎯 Panoramica

Il file `data/parole_smorfia_new.json` contiene il dizionario della Smorfia Napoletana in un formato JSON strutturato e organizzato per lettere, ottimizzato per l'uso nell'applicazione Smorf-IA.

## 📋 Struttura del File

```json
{
  "parole_smorfia": {
    "metadata": {
      "description": "Dizionario della Smorfia Napoletana organizzato per lettere",
      "version": "1.0",
      "total_numbers": 90,
      "created": "2024"
    },
    "A": {
      "Abaco": [48],
      "Abate": [38, 33],
      "Abate in aeroplano": [27],
      "Acqua": [39, 40],
      "Amore": [2, 6],
      ...
    },
    "B": {
      "Baccalà": [77],
      "Bambino": [1, 15],
      "Barca": [17, 47],
      ...
    },
    ...
  }
}
```

## 🔧 Caratteristiche

### ✅ **Organizzazione per Lettere**
- Ogni lettera dell'alfabeto ha la sua sezione
- Facilita la ricerca e la navigazione
- Struttura logica e intuitiva

### ✅ **Formato Standardizzato**
- Ogni parola è una chiave con array di numeri come valore
- Supporta parole con più numeri associati
- Varianti e specificazioni incluse (es: "Abate in aeroplano")

### ✅ **Metadati Inclusi**
- Informazioni sulla versione
- Descrizione del contenuto
- Data di creazione

## 📊 Statistiche Attuali

- **Lettere Disponibili**: A, B, C, D, F, M, P, S, U
- **Parole Totali**: ~800+ parole
- **Numeri Coperti**: 1-90 (completa copertura Smorfia)
- **Varianti**: Include specificazioni e contesti

## 🛠️ Utilizzo

### **1. Caricamento Diretto**

```javascript
// Caricamento del file JSON
fetch('data/parole_smorfia_new.json')
  .then(response => response.json())
  .then(data => {
    console.log('Dati Smorfia caricati:', data);
  });
```

### **2. Utilizzo con SmorfiaUtils**

```javascript
import { SmorfiaUtils } from './js/smorfia-utils.js';

const smorfia = new SmorfiaUtils();

// Cerca una parola
const numeri = smorfia.cercaParola('casa');
console.log('Numeri per "casa":', numeri);

// Cerca per termine parziale
const risultati = smorfia.cercaTermine('amor');
console.log('Parole che contengono "amor":', risultati);

// Ottieni parole per lettera
const paroleA = smorfia.getParolePerLettera('A');
console.log('Parole che iniziano con A:', paroleA);
```

### **3. Ricerca per Numero**

```javascript
// Trova tutte le parole associate al numero 90
const parole90 = smorfia.cercaNumero(90);
console.log('Parole per il numero 90:', parole90);
```

### **4. Analisi Testo**

```javascript
// Analizza un testo e trova parole della Smorfia
const testo = "Ho sognato una casa bianca con fiori";
const analisi = smorfia.analizzaTesto(testo);
console.log('Parole trovate:', analisi);
```

## 🎨 Esempi Pratici

### **Ricerca Semplice**
```javascript
// Cerca "amore"
const numeriAmore = smorfia.cercaParola('amore');
// Risultato: [2, 6]
```

### **Ricerca Avanzata**
```javascript
// Cerca tutte le parole che contengono "cas"
const risultatiCas = smorfia.cercaTermine('cas');
// Risultato: [
//   { parola: "Casa", numeri: [34, 54], lettera: "C" },
//   { parola: "Cascata", numeri: [56], lettera: "C" },
//   ...
// ]
```

### **Generazione Numeri**
```javascript
// Genera numeri da parole chiave
const paroleChiave = ['casa', 'amore', 'fortuna'];
const numeri = smorfia.generaNumeriDaParole(paroleChiave, 5);
// Risultato: [2, 6, 15, 34, 54]
```

## 📁 File Correlati

- **`js/smorfia-utils.js`** - Classe utility per l'utilizzo del JSON
- **`esempio-uso-smorfia.html`** - Esempio interattivo di utilizzo
- **`mobile-audio-test.html`** - Test per funzionalità mobile

## 🔄 Migrazione dal Vecchio Formato

### **Prima (formato testo):**
```
*   Abaco 48
*   Abate-ti 38,33
    *   in aeroplano 27
```

### **Dopo (formato JSON):**
```json
{
  "Abaco": [48],
  "Abate": [38, 33],
  "Abate in aeroplano": [27]
}
```

## 🚀 Vantaggi del Nuovo Formato

### **1. Performance**
- Ricerca più veloce
- Parsing JSON nativo
- Struttura ottimizzata

### **2. Manutenibilità**
- Formato standardizzato
- Facile da aggiornare
- Validazione automatica

### **3. Funzionalità**
- Ricerca per lettera
- Ricerca parziale
- Analisi testo
- Generazione numeri

### **4. Integrazione**
- Compatibile con JavaScript moderno
- Supporto TypeScript
- API RESTful ready

## 🔧 API SmorfiaUtils

### **Metodi Principali**

| Metodo | Descrizione | Parametri | Ritorno |
|--------|-------------|-----------|---------|
| `cercaParola(parola)` | Cerca una parola specifica | `string` | `Array<number>` |
| `cercaTermine(termine)` | Cerca termine parziale | `string` | `Array<Object>` |
| `getParolePerLettera(lettera)` | Ottieni parole per lettera | `string` | `Object` |
| `cercaNumero(numero)` | Cerca per numero | `number` | `Array<Object>` |
| `analizzaTesto(testo)` | Analizza testo completo | `string` | `Array<Object>` |
| `generaNumeriDaParole(parole, qty)` | Genera numeri da parole | `Array, number` | `Array<number>` |
| `getStatistiche()` | Ottieni statistiche | - | `Object` |

### **Proprietà Utility**

| Proprietà | Descrizione | Tipo |
|-----------|-------------|------|
| `isDataLoaded()` | Verifica caricamento dati | `boolean` |
| `getLettereDisponibili()` | Lista lettere disponibili | `Array<string>` |

## 🎯 Utilizzo in Smorf-IA

Il nuovo formato JSON è integrato nell'applicazione Smorf-IA per:

1. **Interpretazione Sogni** - Analisi automatica del testo dei sogni
2. **Ricerca Intelligente** - Suggerimenti e autocompletamento
3. **Generazione Numeri** - Estrazione numeri basata su parole chiave
4. **Navigazione Alfabetica** - Esplorazione per lettere
5. **Statistiche** - Analisi e reportistica

## 📱 Compatibilità

- ✅ **Browser Moderni** - Chrome, Firefox, Safari, Edge
- ✅ **Mobile** - iOS Safari, Chrome Mobile
- ✅ **Node.js** - Supporto server-side
- ✅ **PWA** - Funziona offline

## 🔮 Sviluppi Futuri

- [ ] Aggiunta lettere mancanti (E, G, H, I, L, N, O, Q, R, T, V, Z)
- [ ] Sinonimi e varianti regionali
- [ ] Supporto multilingua
- [ ] API di ricerca avanzata
- [ ] Integrazione AI per suggerimenti intelligenti

---

**Nota**: Questo formato sostituisce il vecchio `parole_smorfia.json` e offre funzionalità molto più avanzate per l'applicazione Smorf-IA.
