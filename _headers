# Headers globali per performance e sicurezza

/*
  X-Frame-Options: DENY
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin

# Service Worker
/sw.js
  Cache-Control: public, max-age=0, must-revalidate
  Service-Worker-Allowed: /

# Manifest PWA
/manifest.json
  Content-Type: application/manifest+json
  Cache-Control: public, max-age=86400

# Risorse statiche - cache lunga
/assets/*
  Cache-Control: public, max-age=31536000, immutable

/css/*
  Cache-Control: public, max-age=31536000, immutable

/js/*
  Cache-Control: public, max-age=31536000, immutable

# API Functions - no cache
/.netlify/functions/*
  Cache-Control: no-cache, no-store, must-revalidate
