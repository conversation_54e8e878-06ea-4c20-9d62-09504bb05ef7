/* SMORFIA.JS - Database e logica della smorfia napoletana */

export class SmorfiaService {
  constructor() {
    this.smorfiaData = null;
    this.isLoaded = false;
  }

  async init() {
    try {
      const response = await fetch('./data/smorfia-ai-ottimizzata.json');
      const data = await response.json();
      this.smorfiaData = data.smorfia;
      this.isLoaded = true;
      console.log('✅ Database smorfia AI ottimizzato caricato');
    } catch (error) {
      console.error('❌ Errore caricamento smorfia AI ottimizzata:', error);
      throw new Error('Impossibile caricare il database della smorfia AI ottimizzata');
    }
  }

  findNumbers(keywords) {
    if (!this.isLoaded || !Array.isArray(keywords)) return [];
    const matches = [];
    const keywordsLower = keywords.map(k => k.toLowerCase());

    for (const [numero, data] of Object.entries(this.smorfiaData)) {
      const simboliLower = data.simboli.map(s => s.toLowerCase());
      const significatoLower = data.significato.toLowerCase();
      const hasMatch = keywordsLower.some(keyword => 
        simboliLower.some(simbolo => simbolo.includes(keyword)) ||
        significatoLower.includes(keyword)
      );
      if (hasMatch) {
        matches.push({
          numero: parseInt(numero),
          significato: data.significato,
          simboli: data.simboli,
          descrizione: data.descrizione
        });
      }
    }
    return matches;
  }

  getNumberInfo(numero) {
    if (!this.isLoaded) {
      console.warn('⚠️ Database smorfia non ancora caricato');
      return null;
    }
    
    const numeroStr = numero.toString();
    const data = this.smorfiaData[numeroStr];
    
    if (!data) {
      console.warn(`⚠️ Numero ${numero} non trovato nel database smorfia`);
      // Debug: stampa tutti i numeri disponibili
      console.log('Numeri disponibili:', Object.keys(this.smorfiaData));
      return null;
    }
    
    return {
      numero: parseInt(numero),
      significato: data.significato,
      simboli: data.simboli,
      descrizione: data.descrizione
    };
  }

  getAllNumbers() {
    return this.smorfiaData ? Object.keys(this.smorfiaData).map(n => parseInt(n)) : [];
  }
}