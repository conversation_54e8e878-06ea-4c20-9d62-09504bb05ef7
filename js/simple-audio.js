/* SIMPLE-AUDIO.JS - Audio Manager Semplificato per Android (Basato su test funzionante) */

export class SimpleAudioManager {
  constructor() {
    this.isRecording = false;
    this.recognition = null;
    this.transcriptText = '';
    this.isAndroid = /Android/i.test(navigator.userAgent);
    this.restartCount = 0;
    
    console.log('🤖 Simple Audio Manager per Android inizializzato');
  }

  async startRecording() {
    try {
      console.log('🎤 Avvio registrazione semplice Android...');
      
      // Step 1: Clean setup (come nel test funzionante)
      this.setupSimpleRecognition();
      
      // Step 2: Start recording
      this.recognition.start();
      this.isRecording = true;
      this.restartCount = 0;
      this.transcriptText = '';
      
      console.log('✅ Registrazione Android avviata (modalità semplice)');
      return true;
      
    } catch (error) {
      console.error('❌ Errore avvio semplice:', error);
      this.isRecording = false;
      throw error;
    }
  }

  setupSimpleRecognition() {
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    
    if (!SpeechRecognition) {
      throw new Error('Speech Recognition non supportato');
    }

    this.recognition = new SpeechRecognition();
    
    // Configurazione IDENTICA al test funzionante
    this.recognition.continuous = false;
    this.recognition.interimResults = false;
    this.recognition.lang = 'it-IT';
    this.recognition.maxAlternatives = 1;

    let fullTranscript = '';

    this.recognition.onstart = () => {
      console.log('🎤 Speech Recognition semplice avviato');
      this.updateSimpleUI(true);
    };

    this.recognition.onresult = (event) => {
      const transcript = event.results[0][0].transcript;
      fullTranscript += transcript + ' ';
      this.transcriptText = fullTranscript;
      this.updateTranscriptionUI();
      
      console.log('✅ Trascrizione ricevuta:', transcript);
      
      // Auto-restart IDENTICO al test (che funziona)
      if (this.isRecording && this.restartCount < 10) {
        setTimeout(() => {
          if (this.isRecording) {
            try {
              this.recognition.start();
              this.restartCount++;
            } catch (error) {
              console.log('Restart:', error.message);
            }
          }
        }, 100);
      }
    };

    this.recognition.onerror = (event) => {
      console.log('⚠️ Errore Speech semplice:', event.error);
      
      // Gestione IDENTICA al test
      if (event.error === 'no-speech' && this.isRecording) {
        console.log('No speech, continuando...');
        setTimeout(() => {
          if (this.isRecording) {
            try { 
              this.recognition.start(); 
            } catch (e) { 
              console.log('No-speech restart:', e.message); 
            }
          }
        }, 100);
        return;
      }
      
      // Altri errori fermano (come nel test)
      this.stopRecording();
      this.showSimpleError(event.error);
    };

    this.recognition.onend = () => {
      console.log('🔚 Speech Recognition terminato');
      
      // Auto-restart IDENTICO al test
      if (this.isRecording && this.restartCount < 10) {
        setTimeout(() => {
          if (this.isRecording) {
            try {
              this.recognition.start();
              this.restartCount++;
            } catch (e) { 
              console.log('End restart:', e.message); 
            }
          }
        }, 100);
      }
    };
  }

  stopRecording() {
    console.log('🛑 Stop registrazione semplice');
    
    this.isRecording = false;
    this.restartCount = 0;
    
    if (this.recognition) {
      try {
        this.recognition.stop();
      } catch (error) {
        console.log('Stop error:', error.message);
      }
      this.recognition = null;
    }
    
    this.updateSimpleUI(false);
    return this.transcriptText;
  }

  updateSimpleUI(recording) {
    const recordBtn = document.getElementById('record-btn');
    const recordingStatus = document.getElementById('recording-status');
    const visualizer = document.getElementById('voice-visualizer');

    if (recordBtn) {
      if (recording) {
        recordBtn.textContent = this.isAndroid ? '🛑 Ferma (Android Semplice)' : '🛑 Ferma';
        recordBtn.classList.add('recording');
      } else {
        recordBtn.textContent = 'Inizia registrazione';
        recordBtn.classList.remove('recording');
      }
    }

    if (recordingStatus) {
      if (recording) {
        recordingStatus.classList.remove('hidden');
      } else {
        recordingStatus.classList.add('hidden');
      }
    }

    if (visualizer) {
      if (recording) {
        visualizer.classList.add('recording');
      } else {
        visualizer.classList.remove('recording');
      }
    }
  }

  updateTranscriptionUI() {
    const transcriptionArea = document.getElementById('transcription-area');
    const transcriptionText = document.getElementById('transcription-text');
    const analyzeBtn = document.getElementById('analyze-voice-btn');

    if (transcriptionArea && transcriptionText && analyzeBtn) {
      if (this.transcriptText.trim()) {
        transcriptionArea.classList.remove('hidden');
        transcriptionText.textContent = this.transcriptText;
        analyzeBtn.disabled = this.transcriptText.length < 10;
      }
    }
  }

  showSimpleError(errorType) {
    const errorMessages = {
      'not-allowed': 'Permessi microfono negati',
      'no-speech': 'Nessun audio rilevato',
      'audio-capture': 'Errore cattura audio',
      'network': 'Errore di rete'
    };

    const message = errorMessages[errorType] || `Errore: ${errorType}`;
    console.error('🤖 Errore Android:', message);
    
    // NO TOAST - può interferire su Android
    alert(`Android: ${message}`);
  }
}

// Export
window.SimpleAudioManager = SimpleAudioManager;