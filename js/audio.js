/* AUDIO.JS - Gestione registrazione e trascrizione audio */

export class AudioManager {
  constructor() {
    this.isRecording = false;
    this.recognition = null;
    this.transcriptText = '';
    this.mediaStream = null;
    this.recordingTimeout = null;
    this.isMobile = this.detectMobile();
    this.isAndroid = /Android/i.test(navigator.userAgent);
    this.chromeVersion = this.getChromeVersion();
    this.permissionGranted = false;
    this.restartAttempts = 0;
    this.maxRestartAttempts = 15; // Aumentato per Android
    this.wakeLock = null;
    this.setupSpeechRecognition();
  }

  getChromeVersion() {
    const match = navigator.userAgent.match(/Chrome\/(\d+)/);
    return match ? parseInt(match[1]) : null;
  }

  detectMobile() {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
           (navigator.maxTouchPoints && navigator.maxTouchPoints > 2 && /MacIntel/.test(navigator.platform));
  }

  setupSpeechRecognition() {
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    if (!SpeechRecognition) {
      console.warn('🎤 Speech Recognition non supportato su questo browser');
      return;
    }

    this.recognition = new SpeechRecognition();

    // Configurazioni specifiche per mobile
    if (this.isMobile) {
      this.recognition.continuous = false; // Mobile browsers work better with non-continuous
      this.recognition.interimResults = false; // Reduce processing on mobile
      console.log('🎤 Configurazione mobile attivata');
    } else {
      this.recognition.continuous = true;
      this.recognition.interimResults = true;
    }

    this.recognition.lang = 'it-IT';
    this.recognition.maxAlternatives = 1;

    this.recognition.onstart = () => {
      console.log('🎤 Speech Recognition avviato');
      this.clearRecordingTimeout();
    };

    this.recognition.onresult = (event) => {
      let finalTranscript = '';
      let interimTranscript = '';

      for (let i = event.resultIndex; i < event.results.length; i++) {
        const transcript = event.results[i][0].transcript;
        if (event.results[i].isFinal) {
          finalTranscript += transcript;
        } else {
          interimTranscript += transcript;
        }
      }

      if (finalTranscript) {
        this.transcriptText += finalTranscript;
        this.updateTranscriptionUI();
        console.log('🎤 Trascrizione finale:', finalTranscript);
      }

      // Restart recording on mobile for continuous effect
      if (this.isMobile && finalTranscript && this.isRecording) {
        setTimeout(() => {
          if (this.isRecording) {
            try {
              this.recognition.start();
            } catch (error) {
              console.log('🎤 Riavvio speech recognition:', error.message);
            }
          }
        }, 100);
      }
    };

    this.recognition.onerror = (event) => {
      console.error('🎤 Errore Speech Recognition:', event.error);

      // Handle specific mobile errors
      if (event.error === 'not-allowed') {
        this.showMobileError('Permesso microfono negato. Controlla le impostazioni del browser.');
      } else if (event.error === 'no-speech') {
        console.log('🎤 Nessun parlato rilevato, continuo...');
        // Don't stop on no-speech for mobile
        if (this.isMobile && this.isRecording) {
          setTimeout(() => {
            if (this.isRecording) {
              try {
                this.recognition.start();
              } catch (error) {
                console.log('🎤 Riavvio dopo no-speech:', error.message);
              }
            }
          }, 100);
        }
        return;
      } else if (event.error === 'network') {
        this.showMobileError('Errore di rete. Verifica la connessione internet.');
      } else {
        this.showMobileError(`Errore microfono: ${event.error}`);
      }

      this.stopRecording();
    };

    this.recognition.onend = () => {
      console.log('🎤 Speech Recognition terminato');
      if (this.isRecording && this.isMobile) {
        // Auto-restart on mobile if still recording
        setTimeout(() => {
          if (this.isRecording) {
            try {
              this.recognition.start();
            } catch (error) {
              console.log('🎤 Riavvio automatico:', error.message);
            }
          }
        }, 100);
      }
    };
  }

  async requestMicrophonePermission() {
    try {
      console.log('🎤 Richiesta permessi microfono...');

      // Check if permissions API is available
      if (navigator.permissions) {
        const permission = await navigator.permissions.query({ name: 'microphone' });
        console.log('🎤 Stato permesso microfono:', permission.state);

        if (permission.state === 'denied') {
          const message = this.isAndroid ? 
            'Vai in Chrome > Impostazioni > Privacy e Sicurezza > Impostazioni sito > Microfono e abilita per questo sito' :
            'Permesso microfono negato dalle impostazioni del browser';
          throw new Error(message);
        }
      }

      // Android-optimized constraints
      const constraints = {
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          ...(this.isMobile && {
            sampleRate: this.isAndroid ? { ideal: 16000 } : 16000,
            channelCount: this.isAndroid ? { ideal: 1 } : 1
          }),
          // Android Chrome specific optimizations
          ...(this.isAndroid && {
            googEchoCancellation: true,
            googAutoGainControl: true,
            googNoiseSuppression: true,
            googHighpassFilter: true,
            googTypingNoiseDetection: false,
            latency: { ideal: 0.1 }
          })
        }
      };

      // Request microphone access
      this.mediaStream = await navigator.mediaDevices.getUserMedia(constraints);

      this.permissionGranted = true;
      console.log('🎤 Permessi microfono concessi', {
        android: this.isAndroid,
        tracks: this.mediaStream.getAudioTracks().length
      });
      
      // Request wake lock for Android
      if (this.isAndroid) {
        await this.requestWakeLock();
      }
      
      return true;

    } catch (error) {
      console.error('🎤 Errore permessi microfono:', error);
      this.permissionGranted = false;

      if (error.name === 'NotAllowedError') {
        const message = this.isAndroid ?
          'Vai in Chrome > Impostazioni > Privacy > Microfono e abilita per questo sito' :
          'Permesso microfono negato. Abilita il microfono nelle impostazioni del browser.';
        throw new Error(message);
      } else if (error.name === 'NotFoundError') {
        throw new Error('Nessun microfono trovato sul dispositivo.');
      } else if (error.name === 'NotReadableError') {
        throw new Error('Microfono già in uso. Chiudi WhatsApp, YouTube o altre app che usano il microfono.');
      } else {
        throw new Error(`Errore accesso microfono: ${error.message}`);
      }
    }
  }

  // Wake lock per Android
  async requestWakeLock() {
    try {
      if ('wakeLock' in navigator && this.isAndroid) {
        this.wakeLock = await navigator.wakeLock.request('screen');
        console.log('🔒 Wake lock Android attivato');
        
        this.wakeLock.addEventListener('release', () => {
          console.log('🔓 Wake lock rilasciato');
        });
      }
    } catch (error) {
      console.log('⚠️ Wake lock non disponibile:', error.message);
    }
  }

  async startRecording() {
    try {
      console.log('🎤 Avvio registrazione...');

      // Check speech recognition support
      if (!this.recognition) {
        throw new Error('Speech Recognition non supportato su questo browser');
      }

      // Request microphone permission first
      if (!this.permissionGranted) {
        await this.requestMicrophonePermission();
      }

      // Clear previous transcript
      this.transcriptText = '';

      // Start speech recognition
      this.recognition.start();
      this.isRecording = true;
      this.updateRecordingUI(true);

      // Set timeout for mobile browsers (they often stop after a few seconds)
      if (this.isMobile) {
        this.setRecordingTimeout();
      }

      console.log('🎤 Registrazione avviata con successo');

    } catch (error) {
      console.error('🎤 Errore avvio registrazione:', error);
      this.isRecording = false;
      this.updateRecordingUI(false);
      throw error;
    }
  }

  setRecordingTimeout() {
    // Clear any existing timeout
    this.clearRecordingTimeout();

    // Set a longer timeout for mobile (30 seconds instead of 4)
    this.recordingTimeout = setTimeout(() => {
      if (this.isRecording) {
        console.log('🎤 Timeout registrazione mobile raggiunto, riavvio...');
        try {
          if (this.recognition) {
            this.recognition.stop();
            setTimeout(() => {
              if (this.isRecording) {
                this.recognition.start();
                this.setRecordingTimeout(); // Set another timeout
              }
            }, 100);
          }
        } catch (error) {
          console.log('🎤 Errore riavvio timeout:', error.message);
        }
      }
    }, 30000); // 30 seconds
  }

  clearRecordingTimeout() {
    if (this.recordingTimeout) {
      clearTimeout(this.recordingTimeout);
      this.recordingTimeout = null;
    }
  }

  stopRecording() {
    console.log('🎤 Fermata registrazione');

    // Clear timeout
    this.clearRecordingTimeout();

    // Stop speech recognition
    if (this.recognition && this.isRecording) {
      try {
        this.recognition.stop();
      } catch (error) {
        console.log('🎤 Errore stop recognition:', error.message);
      }
    }

    // Stop media stream
    if (this.mediaStream) {
      this.mediaStream.getTracks().forEach(track => {
        track.stop();
      });
      this.mediaStream = null;
    }

    this.isRecording = false;
    this.updateRecordingUI(false);

    console.log('🎤 Trascrizione finale:', this.transcriptText);
    return this.transcriptText;
  }

  showMobileError(message) {
    console.error('🎤 Errore mobile:', message);

    // Try to show toast if available
    if (window.smorfiaApp && window.smorfiaApp.showToast) {
      window.smorfiaApp.showToast(message, 'error');
    } else if (window.app && window.app.showToast) {
      window.app.showToast(message, 'error');
    } else {
      // Fallback to alert for mobile
      alert(`Errore Microfono: ${message}`);
    }
  }

  updateRecordingUI(recording) {
    const recordBtn = document.getElementById('record-btn');
    const recordingStatus = document.getElementById('recording-status');
    const visualizer = document.getElementById('voice-visualizer');

    if (recording) {
      recordBtn.textContent = this.isMobile ? 'Ferma (Mobile)' : 'Ferma registrazione';
      recordBtn.classList.add('recording');
      recordingStatus.classList.remove('hidden');
      visualizer.classList.add('recording');
      this.startTimer();

      // Add mobile-specific UI feedback
      if (this.isMobile) {
        const recordingIndicator = document.querySelector('.recording-indicator');
        if (recordingIndicator) {
          recordingIndicator.innerHTML = `
            <span class="recording-dot"></span>
            Registrazione mobile... <span id="recording-timer">00:00</span>
            <br><small>Parla chiaramente</small>
          `;
        }
      }
    } else {
      recordBtn.textContent = 'Inizia registrazione';
      recordBtn.classList.remove('recording');
      recordingStatus.classList.add('hidden');
      visualizer.classList.remove('recording');
      this.stopTimer();

      // Reset mobile UI
      if (this.isMobile) {
        const recordingIndicator = document.querySelector('.recording-indicator');
        if (recordingIndicator) {
          recordingIndicator.innerHTML = `
            <span class="recording-dot"></span>
            In registrazione... <span id="recording-timer">00:00</span>
          `;
        }
      }
    }
  }

  updateTranscriptionUI() {
    const transcriptionArea = document.getElementById('transcription-area');
    const transcriptionText = document.getElementById('transcription-text');
    const analyzeBtn = document.getElementById('analyze-voice-btn');

    if (this.transcriptText.trim()) {
      transcriptionArea.classList.remove('hidden');
      transcriptionText.textContent = this.transcriptText;
      analyzeBtn.disabled = this.transcriptText.length < 10;
    }
  }

  startTimer() {
    const timerElement = document.getElementById('recording-timer');
    let seconds = 0;

    this.recordingTimer = setInterval(() => {
      seconds++;
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      timerElement.textContent = `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }, 1000);
  }

  stopTimer() {
    if (this.recordingTimer) {
      clearInterval(this.recordingTimer);
      this.recordingTimer = null;
    }
  }
}