/* SMORFIA-UTILS.JS - Utilità per il nuovo formato JSON della Smorfia */

export class SmorfiaUtils {
  constructor() {
    this.smorfiaData = null;
    this.loadSmorfiaData();
  }

  async loadSmorfiaData() {
    try {
      const response = await fetch('data/parole_smorfia_new.json');
      this.smorfiaData = await response.json();
      console.log('📚 Dati Smorfia caricati:', Object.keys(this.smorfiaData.parole_smorfia).length, 'lettere');
    } catch (error) {
      console.error('❌ Errore caricamento dati Smorfia:', error);
    }
  }

  /**
   * Cerca una parola nella Smorfia e restituisce i numeri associati
   * @param {string} parola - La parola da cercare
   * @returns {Array} Array di numeri associati alla parola
   */
  cercaParola(parola) {
    if (!this.smorfiaData) {
      console.warn('⚠️ Dati Smorfia non ancora caricati');
      return [];
    }

    const parolaLower = parola.toLowerCase();
    const parolaCapitalized = this.capitalizeFirst(parola);
    
    // Cerca in tutte le lettere
    for (const lettera in this.smorfiaData.parole_smorfia) {
      if (lettera === 'metadata') continue;
      
      const paroleLettera = this.smorfiaData.parole_smorfia[lettera];
      
      // Cerca corrispondenza esatta
      if (paroleLettera[parolaCapitalized]) {
        return paroleLettera[parolaCapitalized];
      }
      
      // Cerca corrispondenza parziale
      for (const chiave in paroleLettera) {
        if (chiave.toLowerCase().includes(parolaLower)) {
          return paroleLettera[chiave];
        }
      }
    }
    
    return [];
  }

  /**
   * Cerca tutte le parole che contengono un termine
   * @param {string} termine - Il termine da cercare
   * @returns {Array} Array di oggetti {parola, numeri}
   */
  cercaTermine(termine) {
    if (!this.smorfiaData) {
      console.warn('⚠️ Dati Smorfia non ancora caricati');
      return [];
    }

    const risultati = [];
    const termineLower = termine.toLowerCase();
    
    for (const lettera in this.smorfiaData.parole_smorfia) {
      if (lettera === 'metadata') continue;
      
      const paroleLettera = this.smorfiaData.parole_smorfia[lettera];
      
      for (const parola in paroleLettera) {
        if (parola.toLowerCase().includes(termineLower)) {
          risultati.push({
            parola: parola,
            numeri: paroleLettera[parola],
            lettera: lettera
          });
        }
      }
    }
    
    return risultati.sort((a, b) => a.parola.localeCompare(b.parola));
  }

  /**
   * Ottiene tutte le parole per una lettera specifica
   * @param {string} lettera - La lettera (A, B, C, etc.)
   * @returns {Object} Oggetto con tutte le parole della lettera
   */
  getParolePerLettera(lettera) {
    if (!this.smorfiaData) {
      console.warn('⚠️ Dati Smorfia non ancora caricati');
      return {};
    }

    const letteraUpper = lettera.toUpperCase();
    return this.smorfiaData.parole_smorfia[letteraUpper] || {};
  }

  /**
   * Ottiene tutte le lettere disponibili
   * @returns {Array} Array delle lettere disponibili
   */
  getLettereDisponibili() {
    if (!this.smorfiaData) {
      console.warn('⚠️ Dati Smorfia non ancora caricati');
      return [];
    }

    return Object.keys(this.smorfiaData.parole_smorfia)
      .filter(lettera => lettera !== 'metadata')
      .sort();
  }

  /**
   * Cerca un numero e restituisce tutte le parole associate
   * @param {number} numero - Il numero da cercare (1-90)
   * @returns {Array} Array di oggetti {parola, lettera}
   */
  cercaNumero(numero) {
    if (!this.smorfiaData) {
      console.warn('⚠️ Dati Smorfia non ancora caricati');
      return [];
    }

    const risultati = [];
    
    for (const lettera in this.smorfiaData.parole_smorfia) {
      if (lettera === 'metadata') continue;
      
      const paroleLettera = this.smorfiaData.parole_smorfia[lettera];
      
      for (const parola in paroleLettera) {
        if (paroleLettera[parola].includes(numero)) {
          risultati.push({
            parola: parola,
            lettera: lettera,
            numeri: paroleLettera[parola]
          });
        }
      }
    }
    
    return risultati.sort((a, b) => a.parola.localeCompare(b.parola));
  }

  /**
   * Analizza un testo e trova tutte le parole della Smorfia
   * @param {string} testo - Il testo da analizzare
   * @returns {Array} Array di oggetti {parola, numeri, posizione}
   */
  analizzaTesto(testo) {
    if (!this.smorfiaData) {
      console.warn('⚠️ Dati Smorfia non ancora caricati');
      return [];
    }

    const risultati = [];
    const parole = testo.toLowerCase().split(/\s+/);
    
    parole.forEach((parola, index) => {
      // Rimuovi punteggiatura
      const parolaClean = parola.replace(/[^\w\s]/gi, '');
      const numeri = this.cercaParola(parolaClean);
      
      if (numeri.length > 0) {
        risultati.push({
          parola: parolaClean,
          numeri: numeri,
          posizione: index,
          parolaOriginale: parola
        });
      }
    });
    
    return risultati;
  }

  /**
   * Genera numeri casuali basati su parole chiave
   * @param {Array} paroleChiave - Array di parole chiave
   * @param {number} quantita - Numero di numeri da generare (default: 5)
   * @returns {Array} Array di numeri unici
   */
  generaNumeriDaParole(paroleChiave, quantita = 5) {
    const numeriTrovati = new Set();
    
    paroleChiave.forEach(parola => {
      const numeri = this.cercaParola(parola);
      numeri.forEach(numero => numeriTrovati.add(numero));
    });
    
    const numeriArray = Array.from(numeriTrovati);
    
    // Se non abbiamo abbastanza numeri, aggiungi numeri casuali
    while (numeriArray.length < quantita) {
      const numeroCasuale = Math.floor(Math.random() * 90) + 1;
      if (!numeriArray.includes(numeroCasuale)) {
        numeriArray.push(numeroCasuale);
      }
    }
    
    // Restituisci solo la quantità richiesta
    return numeriArray.slice(0, quantita).sort((a, b) => a - b);
  }

  /**
   * Ottiene statistiche sui dati della Smorfia
   * @returns {Object} Oggetto con statistiche
   */
  getStatistiche() {
    if (!this.smorfiaData) {
      console.warn('⚠️ Dati Smorfia non ancora caricati');
      return {};
    }

    let totaleParole = 0;
    const statistichePerLettera = {};
    
    for (const lettera in this.smorfiaData.parole_smorfia) {
      if (lettera === 'metadata') continue;
      
      const paroleLettera = Object.keys(this.smorfiaData.parole_smorfia[lettera]).length;
      statistichePerLettera[lettera] = paroleLettera;
      totaleParole += paroleLettera;
    }
    
    return {
      totaleParole,
      totaleLettere: Object.keys(statistichePerLettera).length,
      statistichePerLettera,
      metadata: this.smorfiaData.parole_smorfia.metadata
    };
  }

  /**
   * Capitalizza la prima lettera di una stringa
   * @param {string} str - La stringa da capitalizzare
   * @returns {string} Stringa con prima lettera maiuscola
   */
  capitalizeFirst(str) {
    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
  }

  /**
   * Verifica se i dati sono caricati
   * @returns {boolean} True se i dati sono caricati
   */
  isDataLoaded() {
    return this.smorfiaData !== null;
  }
}

// Esporta un'istanza globale
export const smorfiaUtils = new SmorfiaUtils();
