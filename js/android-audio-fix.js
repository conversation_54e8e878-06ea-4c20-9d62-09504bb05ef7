/* ANDROID AUDIO FIX - Correzioni specifiche per Android */

export class AndroidAudioManager {
  constructor() {
    this.isAndroid = /Android/i.test(navigator.userAgent);
    this.chromeVersion = this.getChromeVersion();
    this.isRecording = false;
    this.recognition = null;
    this.transcriptText = '';
    this.restartAttempts = 0;
    this.maxRestartAttempts = 10;
    this.currentStream = null;
    this.wakeLock = null;
    
    console.log('🤖 Android Audio Manager inizializzato', {
      isAndroid: this.isAndroid,
      chromeVersion: this.chromeVersion
    });
  }

  getChromeVersion() {
    const match = navigator.userAgent.match(/Chrome\/(\d+)/);
    return match ? parseInt(match[1]) : null;
  }

  // Richiesta permessi ottimizzata per Android
  async requestAndroidPermissions() {
    try {
      console.log('🔐 Richiesta permessi Android...');

      // Step 1: Pre-check permissions se disponibile
      if (navigator.permissions) {
        const permission = await navigator.permissions.query({ name: 'microphone' });
        console.log('📋 Stato permesso:', permission.state);
        
        if (permission.state === 'denied') {
          throw new Error('PERMISSION_DENIED');
        }
      }

      // Step 2: Request microphone con constraint Android-specific
      this.currentStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          // Constraint base
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          
          // Android Chrome ottimizzazioni
          sampleRate: { ideal: 16000 },
          channelCount: { ideal: 1 },
          
          // Google-specific per Chrome Android
          googEchoCancellation: true,
          googAutoGainControl: true,
          googNoiseSuppression: true,
          googHighpassFilter: true,
          googTypingNoiseDetection: false,
          googAudioMirroring: false,
          
          // Latency ottimizzazioni
          latency: { ideal: 0.1 },
          volume: { ideal: 1.0 }
        }
      });

      console.log('✅ Stream audio Android ottenuto');
      
      // Keep one track active per Android
      const tracks = this.currentStream.getAudioTracks();
      if (tracks.length > 0) {
        const track = tracks[0];
        console.log('🎤 Track settings:', track.getSettings());
      }
      
      return true;

    } catch (error) {
      console.error('❌ Errore permessi Android:', error);
      
      if (error.message === 'PERMISSION_DENIED' || error.name === 'NotAllowedError') {
        throw new Error('Vai in Chrome > Impostazioni > Privacy > Microfono e abilita per questo sito');
      } else if (error.name === 'NotFoundError') {
        throw new Error('Microfono non trovato. Controlla che non sia disconnesso.');
      } else if (error.name === 'NotReadableError') {
        throw new Error('Microfono occupato. Chiudi altre app che lo usano (YouTube, WhatsApp, ecc.)');
      }
      
      throw error;
    }
  }

  // Setup Speech Recognition ottimizzato per Android
  setupAndroidSpeechRecognition() {
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    
    if (!SpeechRecognition) {
      throw new Error('Speech Recognition non supportato. Usa Chrome 85+ su Android.');
    }

    this.recognition = new SpeechRecognition();
    
    // Configurazioni specifiche Android
    this.recognition.continuous = false;        // CRITICO per Android
    this.recognition.interimResults = false;    // Riduce overhead
    this.recognition.lang = 'it-IT';
    this.recognition.maxAlternatives = 1;
    
    // Android Chrome ha bisogno di questi
    if (this.chromeVersion && this.chromeVersion >= 85) {
      this.recognition.serviceURI = undefined;  // Usa servizio Google
    }

    this.recognition.onstart = () => {
      console.log('🎤 Speech Recognition Android avviato');
      this.restartAttempts = 0;
    };

    this.recognition.onresult = (event) => {
      console.log('📝 Risultato Android ricevuto');
      
      let finalTranscript = '';
      for (let i = event.resultIndex; i < event.results.length; i++) {
        if (event.results[i].isFinal) {
          finalTranscript += event.results[i][0].transcript;
        }
      }

      if (finalTranscript.trim()) {
        this.transcriptText += finalTranscript + ' ';
        this.updateUI();
        console.log('✅ Trascrizione Android:', finalTranscript);
        
        // Auto-restart per Android dopo risultato
        this.scheduleAndroidRestart();
      }
    };

    this.recognition.onerror = (event) => {
      console.error('❌ Errore Speech Android:', event.error);
      
      // Gestione errori specifici Android
      if (event.error === 'no-speech' && this.isRecording) {
        console.log('🔄 No-speech Android, riavvio...');
        this.scheduleAndroidRestart();
        return;
      }
      
      if (event.error === 'audio-capture' && this.isRecording) {
        console.log('🔄 Audio-capture error, riavvio...');
        this.scheduleAndroidRestart();
        return;
      }
      
      if (event.error === 'network' && this.restartAttempts < 3) {
        console.log('🔄 Network error, riavvio...');
        this.scheduleAndroidRestart();
        return;
      }
      
      // Altri errori fermano la registrazione
      this.stopAndroidRecording();
      this.showAndroidError(event.error);
    };

    this.recognition.onend = () => {
      console.log('🔚 Speech Recognition Android terminato');
      
      if (this.isRecording && this.restartAttempts < this.maxRestartAttempts) {
        this.scheduleAndroidRestart();
      } else if (this.restartAttempts >= this.maxRestartAttempts) {
        console.log('⚠️ Troppi riavvii, fermo');
        this.stopAndroidRecording();
      }
    };
  }

  // Riavvio intelligente per Android
  scheduleAndroidRestart() {
    if (!this.isRecording) return;
    
    this.restartAttempts++;
    const delay = Math.min(100 + (this.restartAttempts * 50), 500); // Delay incrementale
    
    console.log(`🔄 Riavvio Android schedulato in ${delay}ms (tentativo ${this.restartAttempts})`);
    
    setTimeout(() => {
      if (this.isRecording && this.recognition) {
        try {
          this.recognition.start();
        } catch (error) {
          console.log('⚠️ Errore riavvio:', error.message);
          if (error.name !== 'InvalidStateError') {
            this.stopAndroidRecording();
          }
        }
      }
    }, delay);
  }

  // Avvio registrazione Android
  async startAndroidRecording() {
    try {
      console.log('🚀 Avvio registrazione Android...');
      
      // Step 1: Request permissions
      await this.requestAndroidPermissions();
      
      // Step 2: Setup recognition
      this.setupAndroidSpeechRecognition();
      
      // Step 3: Request wake lock per Android (se disponibile)
      await this.requestWakeLock();
      
      // Step 4: Clear previous transcript
      this.transcriptText = '';
      this.restartAttempts = 0;
      
      // Step 5: Start recognition
      this.recognition.start();
      this.isRecording = true;
      
      console.log('✅ Registrazione Android avviata');
      return true;
      
    } catch (error) {
      console.error('❌ Errore avvio Android:', error);
      this.isRecording = false;
      throw error;
    }
  }

  // Stop registrazione Android
  stopAndroidRecording() {
    console.log('🛑 Stop registrazione Android');
    
    this.isRecording = false;
    this.restartAttempts = 0;
    
    // Stop recognition
    if (this.recognition) {
      try {
        this.recognition.stop();
      } catch (error) {
        console.log('⚠️ Errore stop recognition:', error.message);
      }
    }
    
    // Stop media stream
    if (this.currentStream) {
      this.currentStream.getTracks().forEach(track => {
        track.stop();
      });
      this.currentStream = null;
    }
    
    // Release wake lock
    if (this.wakeLock) {
      this.wakeLock.release();
      this.wakeLock = null;
    }
    
    return this.transcriptText;
  }

  // Wake lock per evitare che Android vada in sleep
  async requestWakeLock() {
    try {
      if ('wakeLock' in navigator) {
        this.wakeLock = await navigator.wakeLock.request('screen');
        console.log('🔒 Wake lock Android attivato');
        
        this.wakeLock.addEventListener('release', () => {
          console.log('🔓 Wake lock Android rilasciato');
        });
      }
    } catch (error) {
      console.log('⚠️ Wake lock non disponibile:', error.message);
    }
  }

  // Mostra errori Android-specific
  showAndroidError(errorType) {
    const errorMessages = {
      'not-allowed': 'Vai in Chrome > Impostazioni > Privacy e Sicurezza > Impostazioni sito > Microfono, trova questo sito e imposta su "Consenti"',
      'no-speech': 'Nessun audio rilevato. Parla più vicino al microfono.',
      'audio-capture': 'Errore cattura audio. Riavvia Chrome o rimuovi auricolari Bluetooth.',
      'network': 'Errore di rete. Verifica la connessione internet.',
      'service-not-allowed': 'Servizio vocale bloccato. Controlla le impostazioni di privacy di Chrome.',
      'language-not-supported': 'Lingua italiana non supportata dal servizio vocale.',
      'aborted': 'Registrazione interrotta. Riprova.'
    };

    const message = errorMessages[errorType] || `Errore Android: ${errorType}`;
    
    // Mostra toast se disponibile
    if (window.smorfiaApp && window.smorfiaApp.showToast) {
      window.smorfiaApp.showToast(message, 'error');
    } else {
      alert(`🤖 Android: ${message}`);
    }
  }

  // Update UI
  updateUI() {
    // Notify the main app
    if (window.smorfiaApp && window.smorfiaApp.updateVoiceTranscription) {
      window.smorfiaApp.updateVoiceTranscription(this.transcriptText);
    }
    
    // Update transcription area
    const transcriptionArea = document.getElementById('transcription-area');
    const transcriptionText = document.getElementById('transcription-text');
    const analyzeBtn = document.getElementById('analyze-voice-btn');

    if (transcriptionArea && transcriptionText && analyzeBtn) {
      if (this.transcriptText.trim()) {
        transcriptionArea.classList.remove('hidden');
        transcriptionText.textContent = this.transcriptText;
        analyzeBtn.disabled = this.transcriptText.length < 10;
      }
    }
  }

  // Test diagnostico Android
  async runAndroidDiagnostic() {
    const results = {
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      isAndroid: this.isAndroid,
      chromeVersion: this.chromeVersion,
      tests: {}
    };

    try {
      // Test 1: Basic audio
      console.log('🧪 Test 1: Basic Audio Android');
      await this.requestAndroidPermissions();
      results.tests.basicAudio = 'PASS';
      this.currentStream.getTracks().forEach(track => track.stop());
      
      // Test 2: Speech Recognition
      console.log('🧪 Test 2: Speech Recognition Android');
      this.setupAndroidSpeechRecognition();
      results.tests.speechRecognition = 'PASS';
      
      // Test 3: Wake Lock
      console.log('🧪 Test 3: Wake Lock Android');
      await this.requestWakeLock();
      results.tests.wakeLock = this.wakeLock ? 'PASS' : 'SKIP';
      
      console.log('✅ Diagnostico Android completato:', results);
      return results;
      
    } catch (error) {
      console.error('❌ Diagnostico Android fallito:', error);
      results.error = error.message;
      return results;
    }
  }
}

// Export per uso globale
window.AndroidAudioManager = AndroidAudioManager;