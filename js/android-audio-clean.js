/* ANDROID-AUDIO-CLEAN.JS - Versione pulita basata su test funzionante */

export class CleanAudioManager {
  constructor() {
    this.isRecording = false;
    this.recognition = null;
    this.transcriptText = '';
    this.isAndroid = /Android/i.test(navigator.userAgent);
    this.restartCount = 0;
    this.maxRestart = 50; // Aumentato per tolleranza
    this.silenceTimeout = null;
  }

  async startRecording() {
    try {
      this.setupCleanRecognition();
      this.recognition.start();
      this.isRecording = true;
      this.restartCount = 0;
      this.transcriptText = '';
      
      this.updateUI(true);
      return true;
      
    } catch (error) {
      this.isRecording = false;
      throw error;
    }
  }

  setupCleanRecognition() {
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    this.recognition = new SpeechRecognition();
    
    // IDENTICO al test che funziona
    this.recognition.continuous = false;
    this.recognition.interimResults = false;
    this.recognition.lang = 'it-IT';
    this.recognition.maxAlternatives = 1;    let fullTranscript = '';

    this.recognition.onstart = () => {
      console.log('🎤 Clean recognition started');
    };

    this.recognition.onresult = (event) => {
      const transcript = event.results[0][0].transcript;
      fullTranscript += transcript + ' ';
      this.transcriptText = fullTranscript;
      this.updateTranscription();
      
      console.log('✅ Testo ricevuto:', transcript);
      
      // Reset del contatore quando ricevi testo
      this.restartCount = Math.max(0, this.restartCount - 2);
      
      // Riavvio tollerante - sempre continua
      if (this.isRecording && this.restartCount < this.maxRestart) {
        setTimeout(() => {
          if (this.isRecording) {
            try {
              this.recognition.start();
              this.restartCount++;
            } catch (e) {
              console.log('🔄 Restart normale:', e.message);
            }
          }
        }, 300); // Pausa più lunga per Android
      }
    };

    this.recognition.onerror = (event) => {
      console.log('🔊 Speech error:', event.error);
      
      // IGNORA tutti gli errori comuni Android - continua sempre
      if (event.error === 'no-speech' || 
          event.error === 'audio-capture' ||
          event.error === 'network') {
        
        if (this.isRecording) {
          setTimeout(() => {
            if (this.isRecording) {
              try { 
                this.recognition.start(); 
                console.log('🔄 Riavvio dopo:', event.error);
              } catch (e) {}
            }
          }, 200); // Pausa più lunga
        }
        return; // NON fermare mai
      }
      
      // Solo errori critici fermano
      if (event.error === 'not-allowed') {
        this.stopRecording();
        alert('Abilita microfono in Chrome > Impostazioni > Siti');
      }
    };

    this.recognition.onend = () => {
      console.log('🔚 Speech ended, riavvio automatico...');
      
      // Sempre riavvia se sta registrando - tolleranza massima
      if (this.isRecording && this.restartCount < this.maxRestart) {
        setTimeout(() => {
          if (this.isRecording) {
            try {
              this.recognition.start();
              this.restartCount++;
              console.log(`🔄 Riavvio #${this.restartCount}`);
            } catch (e) {
              console.log('🔄 End restart:', e.message);
            }
          }
        }, 250); // Pausa breve per continuita
      } else if (this.restartCount >= this.maxRestart) {
        console.log('⚠️ Limite riavvii raggiunto, ma trascrizione salvata');
        this.stopRecording();
      }
    };
  }

  stopRecording() {
    this.isRecording = false;
    if (this.recognition) {
      try { this.recognition.stop(); } catch (e) {}
    }
    this.updateUI(false);
    return this.transcriptText;
  }

  updateUI(recording) {
    const recordBtn = document.getElementById('record-btn');
    const recordingStatus = document.getElementById('recording-status');
    const visualizer = document.getElementById('voice-visualizer');
    
    if (recordBtn) {
      if (recording) {
        recordBtn.textContent = '🛑 Ferma (Registrazione Libera)';
        recordBtn.classList.add('recording');
        recordBtn.style.background = '#38a169'; // Verde per indicare attivo
      } else {
        recordBtn.textContent = 'Inizia registrazione';
        recordBtn.classList.remove('recording');
        recordBtn.style.background = '';
      }
    }
    
    if (recordingStatus) {
      recording ? recordingStatus.classList.remove('hidden') : recordingStatus.classList.add('hidden');
      
      // Feedback per Android
      if (recording) {
        const indicator = recordingStatus.querySelector('.recording-indicator');
        if (indicator && this.isAndroid) {
          indicator.innerHTML = `
            <span class="recording-dot"></span>
            🎤 Registrazione Android LIBERA - Parla con pause naturali
            <br><small>Riavvii: ${this.restartCount}/${this.maxRestart}</small>
          `;
        }
      }
    }
    
    if (visualizer) {
      recording ? visualizer.classList.add('recording') : visualizer.classList.remove('recording');
    }
  }

  updateTranscription() {
    const transcriptionText = document.getElementById('transcription-text');
    const transcriptionArea = document.getElementById('transcription-area');
    const analyzeBtn = document.getElementById('analyze-voice-btn');
    
    if (transcriptionText) transcriptionText.textContent = this.transcriptText;
    if (transcriptionArea) transcriptionArea.classList.remove('hidden');
    if (analyzeBtn) analyzeBtn.disabled = this.transcriptText.length < 10;
  }
}